Benchmarks
==========

Since I'm obsessed with performance, here is a tiny benchmark comparing the performance of the 0.3.1 version and the current version written in ES6

| Description                  | Library         | Op/s       |  %   |
|------------------------------|-----------------|-----------:|-----:|
| simple dependency chains     | 0.3.1 version   | 66,722.22  | 8%   |
|                              | current version | 837,416.60 | 100% |
| slightly more complex chains | 0.3.1 version   | 24,530.85  | 6%   |
|                              | current version | 386,620.50 | 100% |
