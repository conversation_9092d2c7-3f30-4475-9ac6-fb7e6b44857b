import express from "express";
import {body, validationResult} from "express-validator";
import  Event  from "../models/Event.js";
import { authenticateToken } from "../middleware/auth.js";

const router = express.Router();


const validateEvent = [
  body("title")
    .trim()
    .isLength({ min: 1, max: 255 })
    .withMessage("Title is required and must be between 1 and 255 characters"),
  body("description")
    .optional()
    .trim()
    .isLength({ max: 1000 })
    .withMessage("Description must be less than 1000 characters"),
  body("location")
    .optional()
    .trim()
    .isLength({ max: 255 })
    .withMessage("Location must be less than 255 characters"),
  body("eventDate").isISO8601().withMessage("Event date must be a valid date"),
  body("isPublic")
    .optional()
    .isBoolean()
    .withMessage("isPublic must be a boolean value"),
];

const validateEventQuery = [
  query("filter")
    .optional()
    .isIn(["upcoming", "past", "all"])
    .withMessage("Filter must be one of: upcoming, past, all"),
  query("limit")
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage("Limit must be between 1 and 100"),
  query("offset")
    .optional()
    .isInt({ min: 0 })
    .withMessage("Offset must be a non-negative integer"),
];


router.get("/", authenticateToken, validateEventQuery, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: "Validation failed",
        details: errors.array(),
      });
    }

    const { filter = "all", limit = 50, offset = 0 } = req.query;
    const userId = req.user._id;

    let query = { userId };
    const now = new Date();

   
    if (filter === "upcoming") {
      query.eventDate = { $gte: now };
    } else if (filter === "past") {
      query.eventDate = { $lt: now };
    }

    const events = await Event.find(query)
      .sort({ eventDate: 1 })
      .limit(parseInt(limit))
      .skip(parseInt(offset));

    const total = await Event.countDocuments(query);

    res.json({
      events,
      total,
      filter,
      limit: parseInt(limit),
      offset: parseInt(offset),
    });
  } catch (error) {
    console.error("Get events error:", error);
    res.status(500).json({ error: "Failed to fetch events" });
  }
});


router.get("/:id", authenticateToken, async (req, res) => {
  try {
    const eventId = req.params.id;
    const userId = req.user._id;

    const event = await Event.findOne({
      _id: eventId,
      userId,
    });

    if (!event) {
      return res.status(404).json({ error: "Event not found" });
    }

    res.json({ event });
  } catch (error) {
    console.error("Get event error:", error);
    res.status(500).json({ error: "Failed to fetch event" });
  }
});


router.post("/", authenticateToken, validateEvent, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: "Validation failed",
        details: errors.array(),
      });
    }

    const {
      title,
      description,
      location,
      eventDate,
      isPublic = false,
    } = req.body;
    const userId = req.user._id;

    const event = await Event.create({
      title,
      description,
      location,
      eventDate,
      isPublic,
      userId,
    });

    res.status(201).json({
      message: "Event created successfully",
      event,
    });
  } catch (error) {
    console.error("Create event error:", error);
    res.status(500).json({ error: "Failed to create event" });
  }
});


router.put("/:id", authenticateToken, validateEvent, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: "Validation failed",
        details: errors.array(),
      });
    }

    const eventId = req.params.id;
    const userId = req.user._id;
    const { title, description, location, eventDate, isPublic } = req.body;

    const event = await Event.findOne({
      _id: eventId,
      userId,
    });

    if (!event) {
      return res.status(404).json({ error: "Event not found" });
    }

    Object.assign(event, {
      title,
      description,
      location,
      eventDate,
      isPublic,
    });

    await event.save();

    res.json({
      message: "Event updated successfully",
      event,
    });
  } catch (error) {
    console.error("Update event error:", error);
    res.status(500).json({ error: "Failed to update event" });
  }
});


router.delete("/:id", authenticateToken, async (req, res) => {
  try {
    const eventId = req.params.id;
    const userId = req.user._id;

    const event = await Event.findOne({
      _id: eventId,
      userId,
    });

    if (!event) {
      return res.status(404).json({ error: "Event not found" });
    }

    await Event.findByIdAndDelete(eventId);

    res.json({ message: "Event deleted successfully" });
  } catch (error) {
    console.error("Delete event error:", error);
    res.status(500).json({ error: "Failed to delete event" });
  }
});


router.get("/public/:shareId", async (req, res) => {
  try {
    const { shareId } = req.params;

    const event = await Event.findOne({
      shareId,
      isPublic: true,
    }).populate("userId", "firstName lastName");

    if (!event) {
      return res.status(404).json({ error: "Public event not found" });
    }

    res.json({ event });
  } catch (error) {
    console.error("Get public event error:", error);
    res.status(500).json({ error: "Failed to fetch public event" });
  }
});

export default router;