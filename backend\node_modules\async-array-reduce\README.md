# async-array-reduce [![NPM version](https://img.shields.io/npm/v/async-array-reduce.svg?style=flat)](https://www.npmjs.com/package/async-array-reduce) [![NPM downloads](https://img.shields.io/npm/dm/async-array-reduce.svg?style=flat)](https://npmjs.org/package/async-array-reduce) [![Build Status](https://img.shields.io/travis/jonschlinkert/async-array-reduce.svg?style=flat)](https://travis-ci.org/jonschlinkert/async-array-reduce)

Async reduce.

## Install

Install with [npm](https://www.npmjs.com/):

```sh
$ npm install --save async-array-reduce
```

## Usage

```js
var reduce = require('async-array-reduce');

reduce(['a', 'b', 'c'], [], function (acc, val, next) {
  acc.push(val + val);
  next(null, acc);
}, function (err, arr) {
  //=> ['aa', 'bb', 'cc']
});
```

## About

### Related projects

* [arr-filter](https://www.npmjs.com/package/arr-filter): Faster alternative to javascript's native filter method. | [homepage](https://github.com/jonschlinkert/arr-filter "Faster alternative to javascript's native filter method.")
* [arr-flatten](https://www.npmjs.com/package/arr-flatten): Recursively flatten an array or arrays. This is the fastest implementation of array flatten. | [homepage](https://github.com/jonschlinkert/arr-flatten "Recursively flatten an array or arrays. This is the fastest implementation of array flatten.")
* [arr-reduce](https://www.npmjs.com/package/arr-reduce): Fast array reduce that also loops over sparse elements. | [homepage](https://github.com/jonschlinkert/arr-reduce "Fast array reduce that also loops over sparse elements.")
* [array-unique](https://www.npmjs.com/package/array-unique): Remove duplicate values from an array. Fastest ES5 implementation. | [homepage](https://github.com/jonschlinkert/array-unique "Remove duplicate values from an array. Fastest ES5 implementation.")

### Contributing

Pull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).

### Building docs

_(This document was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme) (a [verb](https://github.com/verbose/verb) generator), please don't edit the readme directly. Any changes to the readme must be made in [.verb.md](.verb.md).)_

To generate the readme and API documentation with [verb](https://github.com/verbose/verb):

```sh
$ npm install -g verb verb-generate-readme && verb
```

### Running tests

Install dev dependencies:

```sh
$ npm install -d && npm test
```

### Author

**Jon Schlinkert**

* [github/jonschlinkert](https://github.com/jonschlinkert)
* [twitter/jonschlinkert](http://twitter.com/jonschlinkert)

### License

Copyright © 2016, [Jon Schlinkert](https://github.com/jonschlinkert).
Released under the [MIT license](https://github.com/jonschlinkert/async-array-reduce/blob/master/LICENSE).

***

_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.1.28, on August 02, 2016._