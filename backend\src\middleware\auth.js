import jwt from "jsonwebtoken";
import User from "../models/User.js";

const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;

    const token = authHeader && authHeader.split(" ")[1];

    if(!token){
      return res.status(401).json({
        error: "Access token required"
      });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    const user = await User.findById(decoded.userId);
    if(!user){
      return res.status(401).json({
        error: "User not found"
      });
    }

    req.user = user;
    next();
  } catch (error) {
    if (error.name === "JsonWebTokenError"){
      return res.status(401).json({
        error: "Invalid token"
      });
    }
    if(error.name === "TokenExpiredError") {
      return res.status(401).json({
        error: "Token expired"
      });
    }

    console.error("Auth middleware error:", error);
    return res.status(500).json({
      error: "Authentication failed"
    });
  }
};

const generateToken = (userId) => {
  return jwt.sign({ userId}, process.env.JWT_SECRET, {
    expiresIn: process.env.JWT_EXPIRES_IN || "30d",
  });
};

export { authenticateToken, generateToken };
