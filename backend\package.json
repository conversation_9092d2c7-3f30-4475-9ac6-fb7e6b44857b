{"name": "backend", "version": "1.0.0", "description": "", "main": "index.js", "type": "module", "scripts": {"dev": "nodemon dotenv/config src/server.js", "start": "node dotenv/config src/server.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.2.2", "express": "^5.1.0", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.18.2", "morgan": "^1.10.1", "uuid": "^13.0.0"}, "devDependencies": {"nodemon": "^3.1.10"}}