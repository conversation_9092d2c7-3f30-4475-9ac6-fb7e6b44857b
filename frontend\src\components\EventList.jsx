import React from 'react';
import { formatDateTime, getRelativeTime, isUpcoming } from '../utils/dateUtils';
import './EventList.css';

const EventList = ({ events, onEdit, onDelete }) => {
  if (events.length === 0) {
    return (
      <div className="empty-state">
        <div className="empty-icon">📅</div>
        <h3>No events found</h3>
        <p>Create your first event to get started!</p>
      </div>
    );
  }

  const copyShareLink = (event) => {
    if (event.isPublic && event.shareId) {
      const shareUrl = `${window.location.origin}/event/${event.shareId}`;
      navigator.clipboard.writeText(shareUrl).then(() => {
        alert('Share link copied to clipboard!');
      }).catch(() => {
        alert(`Share link: ${shareUrl}`);
      });
    }
  };

  return (
    <div className="event-list">
      {events.map(event => (
        <div key={event.id} className={`event-card ${isUpcoming(event.eventDate) ? 'upcoming' : 'past'}`}>
          <div className="event-header">
            <h3 className="event-title">{event.title}</h3>
            <div className="event-actions">
              {event.isPublic && event.shareId && (
                <button
                  onClick={() => copyShareLink(event)}
                  className="share-btn"
                  title="Copy share link"
                >
                  🔗
                </button>
              )}
              <button
                onClick={() => onEdit(event)}
                className="edit-btn"
                title="Edit event"
              >
                ✏️
              </button>
              <button
                onClick={() => onDelete(event.id)}
                className="delete-btn"
                title="Delete event"
              >
                🗑️
              </button>
            </div>
          </div>

          <div className="event-details">
            <div className="event-date">
              <span className="date-label">📅</span>
              <div className="date-info">
                <span className="date-primary">{formatDateTime(event.eventDate)}</span>
                <span className="date-relative">{getRelativeTime(event.eventDate)}</span>
              </div>
            </div>

            {event.location && (
              <div className="event-location">
                <span className="location-label">📍</span>
                <span>{event.location}</span>
              </div>
            )}

            {event.description && (
              <div className="event-description">
                <p>{event.description}</p>
              </div>
            )}

            <div className="event-meta">
              <span className={`event-status ${isUpcoming(event.eventDate) ? 'upcoming' : 'past'}`}>
                {isUpcoming(event.eventDate) ? 'Upcoming' : 'Past'}
              </span>
              {event.isPublic && (
                <span className="public-badge">Public</span>
              )}
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default EventList;
