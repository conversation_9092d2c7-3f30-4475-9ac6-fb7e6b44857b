.event-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.event-card {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.event-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.event-card.upcoming {
  border-left: 4px solid #28a745;
}

.event-card.past {
  border-left: 4px solid #6c757d;
  opacity: 0.8;
}

.event-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
  gap: 1rem;
}

.event-title {
  margin: 0;
  color: #333;
  font-size: 1.3rem;
  font-weight: 600;
  flex: 1;
}

.event-actions {
  display: flex;
  gap: 0.5rem;
  flex-shrink: 0;
}

.event-actions button {
  background: none;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 0.5rem;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.2s ease;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.share-btn:hover {
  background: #e3f2fd;
  border-color: #2196f3;
}

.edit-btn:hover {
  background: #fff3e0;
  border-color: #ff9800;
}

.delete-btn:hover {
  background: #ffebee;
  border-color: #f44336;
}

.event-details {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.event-date {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.date-label {
  font-size: 1.2rem;
  flex-shrink: 0;
}

.date-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.date-primary {
  font-weight: 600;
  color: #333;
  font-size: 1rem;
}

.date-relative {
  font-size: 0.9rem;
  color: #666;
}

.event-location {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: #666;
  font-size: 0.95rem;
}

.location-label {
  font-size: 1.1rem;
  flex-shrink: 0;
}

.event-description {
  margin-top: 0.5rem;
}

.event-description p {
  margin: 0;
  color: #555;
  line-height: 1.5;
  font-size: 0.95rem;
}

.event-meta {
  display: flex;
  gap: 0.75rem;
  margin-top: 0.5rem;
  flex-wrap: wrap;
}

.event-status {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.event-status.upcoming {
  background: #d4edda;
  color: #155724;
}

.event-status.past {
  background: #f8f9fa;
  color: #6c757d;
}

.public-badge {
  background: #e3f2fd;
  color: #1976d2;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  color: #666;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.empty-state h3 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 1.5rem;
}

.empty-state p {
  margin: 0;
  font-size: 1rem;
}

/* Responsive design */
@media (max-width: 768px) {
  .event-card {
    padding: 1rem;
  }
  
  .event-header {
    flex-direction: column;
    align-items: stretch;
    gap: 0.75rem;
  }
  
  .event-actions {
    justify-content: flex-end;
  }
  
  .event-title {
    font-size: 1.1rem;
  }
  
  .date-info {
    gap: 0.1rem;
  }
  
  .date-primary {
    font-size: 0.9rem;
  }
  
  .date-relative {
    font-size: 0.8rem;
  }
  
  .empty-state {
    padding: 2rem 1rem;
  }
  
  .empty-icon {
    font-size: 3rem;
  }
  
  .empty-state h3 {
    font-size: 1.2rem;
  }
}
