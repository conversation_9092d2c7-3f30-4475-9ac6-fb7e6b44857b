{"name": "has-glob", "description": "Returns `true` if an array has a glob pattern.", "version": "1.0.0", "homepage": "https://github.com/jonschlinkert/has-glob", "author": "<PERSON> (https://github.com/jonschlinkert)", "repository": "jonschlinkert/has-glob", "bugs": {"url": "https://github.com/jonschlinkert/has-glob/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"is-glob": "^3.0.0"}, "devDependencies": {"gulp-format-md": "^0.1.10", "mocha": "^3.0.2"}, "keywords": ["bash", "braces", "check", "exec", "expression", "extglob", "glob", "globbing", "globstar", "has", "match", "matches", "pattern", "regex", "regular", "string", "test"], "verb": {"related": {"list": ["has-glob", "is-glob", "is-negated-glob", "is-valid-glob", "micromatch"]}, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "reflinks": ["verb", "verb-generate-readme"]}}