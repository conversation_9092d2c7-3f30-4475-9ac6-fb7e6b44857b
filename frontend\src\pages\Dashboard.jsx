import React, { useState, useEffect } from 'react';
import { eventsAPI } from '../services/api';
import EventList from '../components/EventList';
import EventForm from '../components/EventForm';
import './Dashboard.css';

const Dashboard = () => {
  const [events, setEvents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [filter, setFilter] = useState('all');
  const [showForm, setShowForm] = useState(false);
  const [editingEvent, setEditingEvent] = useState(null);

  useEffect(() => {
    fetchEvents();
  }, [filter]);

  const fetchEvents = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await eventsAPI.getEvents({ filter });
      setEvents(response.data.events);
    } catch (error) {
      console.error('Error fetching events:', error);
      setError('Failed to load events. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateEvent = () => {
    setEditingEvent(null);
    setShowForm(true);
  };

  const handleEditEvent = (event) => {
    setEditingEvent(event);
    setShowForm(true);
  };

  const handleDeleteEvent = async (eventId) => {
    if (!window.confirm('Are you sure you want to delete this event?')) {
      return;
    }

    try {
      await eventsAPI.deleteEvent(eventId);
      setEvents(events.filter(event => event.id !== eventId));
    } catch (error) {
      console.error('Error deleting event:', error);
      alert('Failed to delete event. Please try again.');
    }
  };

  const handleFormSubmit = async (eventData) => {
    try {
      if (editingEvent) {
        // Update existing event
        const response = await eventsAPI.updateEvent(editingEvent.id, eventData);
        setEvents(events.map(event => 
          event.id === editingEvent.id ? response.data.event : event
        ));
      } else {
        // Create new event
        const response = await eventsAPI.createEvent(eventData);
        setEvents([response.data.event, ...events]);
      }
      
      setShowForm(false);
      setEditingEvent(null);
    } catch (error) {
      console.error('Error saving event:', error);
      throw error; // Let the form handle the error display
    }
  };

  const handleFormCancel = () => {
    setShowForm(false);
    setEditingEvent(null);
  };

  const getFilteredEventsCount = () => {
    const now = new Date();
    const upcoming = events.filter(event => new Date(event.eventDate) >= now).length;
    const past = events.filter(event => new Date(event.eventDate) < now).length;
    
    return { upcoming, past, total: events.length };
  };

  const counts = getFilteredEventsCount();

  if (showForm) {
    return (
      <div className="dashboard">
        <div className="dashboard-header">
          <h1>{editingEvent ? 'Edit Event' : 'Create New Event'}</h1>
        </div>
        
        <EventForm
          event={editingEvent}
          onSubmit={handleFormSubmit}
          onCancel={handleFormCancel}
        />
      </div>
    );
  }

  return (
    <div className="dashboard">
      <div className="dashboard-header">
        <h1>My Events</h1>
        <button 
          onClick={handleCreateEvent}
          className="create-event-btn"
        >
          + Create Event
        </button>
      </div>

      <div className="dashboard-stats">
        <div className="stat-card">
          <span className="stat-number">{counts.total}</span>
          <span className="stat-label">Total Events</span>
        </div>
        <div className="stat-card">
          <span className="stat-number">{counts.upcoming}</span>
          <span className="stat-label">Upcoming</span>
        </div>
        <div className="stat-card">
          <span className="stat-number">{counts.past}</span>
          <span className="stat-label">Past Events</span>
        </div>
      </div>

      <div className="dashboard-filters">
        <button 
          onClick={() => setFilter('all')}
          className={`filter-btn ${filter === 'all' ? 'active' : ''}`}
        >
          All Events
        </button>
        <button 
          onClick={() => setFilter('upcoming')}
          className={`filter-btn ${filter === 'upcoming' ? 'active' : ''}`}
        >
          Upcoming
        </button>
        <button 
          onClick={() => setFilter('past')}
          className={`filter-btn ${filter === 'past' ? 'active' : ''}`}
        >
          Past Events
        </button>
      </div>

      <div className="dashboard-content">
        {loading ? (
          <div className="loading">Loading events...</div>
        ) : error ? (
          <div className="error-message">
            {error}
            <button onClick={fetchEvents} className="retry-btn">
              Try Again
            </button>
          </div>
        ) : (
          <EventList
            events={events}
            onEdit={handleEditEvent}
            onDelete={handleDeleteEvent}
          />
        )}
      </div>
    </div>
  );
};

export default Dashboard;
