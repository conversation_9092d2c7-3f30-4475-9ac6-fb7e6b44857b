import mongoose from "mongoose";
import dotenv from "dotenv";
dotenv.config({
  path: "./.env"
})

const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log("mongodb connected scuccessfully");
  } catch (error) {
    console.error(error);
    process.exit(1);
  
  }
}

export default connectDB;