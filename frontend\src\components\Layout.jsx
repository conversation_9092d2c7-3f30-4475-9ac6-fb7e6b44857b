import { useAuth } from '../context/AuthContext';
import './Layout.css';

const Layout = ({ children }) => {
  const { user, logout } = useAuth();

  const handleLogout = () => {
    logout();
  };

  return (
    <div className="layout">
      <header className="header">
        <div className="header-content">
          <h1 className="logo">
            <span className="logo-icon">📅</span>
            Event Tracker
          </h1>
          
          {user && (
            <div className="user-menu">
              <span className="user-greeting">
                Hello, {user.firstName || user.email}!
              </span>
              <button 
                onClick={handleLogout}
                className="logout-btn"
              >
                Logout
              </button>
            </div>
          )}
        </div>
      </header>

      <main className="main-content">
        {children}
      </main>

      <footer className="footer">
        <p>&copy; 2024 Mini Event Tracker. Built with React & Node.js</p>
      </footer>
    </div>
  );
};

export default Layout;
