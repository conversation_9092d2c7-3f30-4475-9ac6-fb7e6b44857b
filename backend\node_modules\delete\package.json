{"name": "delete", "description": "Delete files and folders and any intermediate directories if they exist (sync and async).", "version": "1.1.0", "homepage": "https://github.com/jonschlinkert/delete", "author": "<PERSON> (https://github.com/jonschlinkert)", "repository": "jonschlinkert/delete", "bugs": {"url": "https://github.com/jonschlinkert/delete/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.12.0"}, "scripts": {"test": "mocha"}, "dependencies": {"async-each": "^1.0.1", "extend-shallow": "^2.0.1", "matched": "^1.0.2", "rimraf": "^2.6.1"}, "devDependencies": {"gulp-format-md": "^1.0.0", "mocha": "^3.4.2"}, "keywords": ["check", "clean", "cleaning", "cleanup", "cwd", "del", "delete", "destroy", "dir", "directory", "file", "filepath", "files", "filesystem", "folder", "fs", "glob", "inside", "path", "pwd", "relative", "remove", "<PERSON><PERSON><PERSON>", "rm", "rmdir", "rmrf", "trash", "unlink"], "verb": {"related": {"list": ["copy", "export-files", "micromatch", "write"]}, "plugins": ["gulp-format-md"], "reflinks": ["grunt", "verb"], "layout": "default", "toc": false, "tasks": ["readme"], "lint": {"reflinks": true}}}