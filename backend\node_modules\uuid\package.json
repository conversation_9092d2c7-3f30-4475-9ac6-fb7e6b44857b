{"name": "uuid", "version": "13.0.0", "description": "RFC9562 UUIDs", "type": "module", "funding": ["https://github.com/sponsors/broofa", "https://github.com/sponsors/ctavan"], "commitlint": {"extends": ["@commitlint/config-conventional"]}, "keywords": ["uuid", "guid", "rfc4122", "rfc9562"], "license": "MIT", "bin": {"uuid": "./dist-node/bin/uuid"}, "sideEffects": false, "types": "./dist/index.d.ts", "exports": {".": {"node": "./dist-node/index.js", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "files": ["dist", "dist-node", "!**/test"], "devDependencies": {"@babel/eslint-parser": "7.27.1", "@commitlint/cli": "19.8.0", "@commitlint/config-conventional": "19.8.0", "@eslint/js": "9.26.0", "bundlewatch": "0.4.1", "commander": "13.1.0", "eslint": "9.26.0", "eslint-config-prettier": "10.1.2", "eslint-plugin-prettier": "5.4.0", "globals": "16.0.0", "husky": "9.1.7", "jest": "29.7.0", "lint-staged": "15.5.2", "neostandard": "0.12.1", "npm-run-all": "4.1.5", "prettier": "3.5.3", "release-please": "17.0.0", "runmd": "1.4.1", "standard-version": "9.5.0", "typescript": "5.2.2", "typescript-eslint": "8.32.0"}, "optionalDevDependencies": {"@wdio/browserstack-service": "9.2.1", "@wdio/cli": "9.2.1", "@wdio/jasmine-framework": "9.2.1", "@wdio/local-runner": "9.2.1", "@wdio/spec-reporter": "9.1.3", "@wdio/static-server-service": "9.1.3"}, "scripts": {"build": "./scripts/build.sh", "build:watch": "tsc --watch -p tsconfig.json", "bundlewatch": "npm run pretest:browser && bundlewatch --config bundlewatch.config.json", "docs:diff": "npm run docs && git diff --quiet -I \"[0-9a-f-]{36}\" README.md", "docs": "npm run build && npx runmd --output=README.md README_js.md", "eslint:check": "eslint src/ test/ examples/ *.[jt]s", "eslint:fix": "eslint --fix src/ test/ examples/ *.[jt]s", "examples:browser:rollup:build": "cd examples/browser-rollup && npm run build", "examples:browser:webpack:build": "cd examples/browser-webpack && npm run build", "examples:node:esmodules:test": "cd examples/node-esmodules && npm test", "examples:node:jest:test": "cd examples/node-jest && npm test", "examples:node:typescript:test": "cd examples/typescript && npm test", "lint": "npm run eslint:check && npm run prettier:check", "md": "runmd --watch --output=README.md README_js.md", "prepack": "npm run build -- --no-pack", "prepare": "husky", "prepublishOnly": "npm run build", "pretest:benchmark": "npm run build", "pretest:browser": "./scripts/iodd && npm run build && npm-run-all --parallel examples:browser:**", "pretest:node": "npm run build", "pretest": "npm run build", "prettier:check": "prettier --check .", "prettier:fix": "prettier --write .", "release": "standard-version --no-verify", "test:benchmark": "cd examples/benchmark && npm test", "test:browser": "wdio run ./wdio.conf.js", "test:node": "npm-run-all --parallel examples:node:**", "test:watch": "node --test --enable-source-maps --watch dist-node/test/*.js", "test": "node --test --enable-source-maps dist-node/test/*.js"}, "repository": {"type": "git", "url": "https://github.com/uuidjs/uuid.git"}, "lint-staged": {"*": ["prettier --no-error-on-unmatched-pattern --write"], "*.{js,jsx}": ["eslint --no-error-on-unmatched-pattern --fix"]}, "standard-version": {"scripts": {"postchangelog": "prettier --write CHANGELOG.md"}}, "packageManager": "npm@11.3.0"}