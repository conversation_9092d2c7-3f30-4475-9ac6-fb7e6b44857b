{"name": "expand-tilde", "description": "Bash-like tilde expansion for node.js. Expands a leading tilde in a file path to the user home directory, or `~+` to the cwd.", "version": "2.0.2", "homepage": "https://github.com/jonschlinkert/expand-tilde", "author": "<PERSON> (https://github.com/jonschlinkert)", "repository": "jonschlinkert/expand-tilde", "bugs": {"url": "https://github.com/jonschlinkert/expand-tilde/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "devDependencies": {"gulp-format-md": "^0.1.9", "is-windows": "^0.2.0", "mocha": "^2.5.3"}, "keywords": ["cwd", "expand", "expansion", "filepath", "home", "path", "pwd", "tilde", "user", "userhome"], "verb": {"run": true, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["braces", "expand-brackets", "is-glob", "micromatch"]}, "reflinks": ["verb"], "lint": {"reflinks": true}}, "dependencies": {"homedir-polyfill": "^1.0.1"}}