import mongoose from "mongoose";
import { v4 as uuidv4 } from 'uuid';

const eventSchema = new mongoose.Schema({
   title: {
    type: String,
    required: true,
    trim: true,
    maxlength: 255,
    minlength: 1,
   },
   description: {
    type: String,
    required: true,
    trim: true,
    maxlength: 1000,
   },
   location: {
    type: String,
    required: true,
    trim: true,
    maxlength: 255,
   },
   eventDate: {
    type: Date,
    required: true,
   },
   userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
    required: true,
   },
   shareId: {
    type: String,
    required: true,
    unique: true,
    sparse: true,
   },
   isPublic: {
    type: Boolean,
    default: false,
   }
}, {
  timestamps: true,
});


eventSchema.pre("save", function(next) {
  if (this.isPublic && !this.shareId) {
    this.shareId = uuidv4();
  } else if (!this.isPublic) {
    this.shareId = undefined;
  }
  next();
})


eventSchema.methods.isPast = function () {
  return new date(this.eventDate) < new Date();
};

eventSchema.methods.isUpcoming = function () {
  return new Date(this.eventDate) >= new Date();
};


eventSchema.index({
  userId: 1, eventDate: 1
});

// const Event = mongoose.model("Event", eventSchema);

// export default Event;

// User.js
export const Event = mongoose.model('Event', eventSchema);


