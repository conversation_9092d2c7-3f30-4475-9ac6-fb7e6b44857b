.public-event-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 2rem 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.public-event-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  padding: 2.5rem;
  max-width: 600px;
  width: 100%;
}

.event-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.event-badge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #e3f2fd;
  color: #1976d2;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
}

.badge-icon {
  font-size: 1rem;
}

.event-status {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.event-status.upcoming {
  background: #d4edda;
  color: #155724;
}

.event-status.past {
  background: #f8f9fa;
  color: #6c757d;
}

.event-title {
  margin: 0 0 2rem 0;
  color: #333;
  font-size: 2.2rem;
  font-weight: 700;
  line-height: 1.2;
}

.event-details {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.detail-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
  margin-top: 0.1rem;
}

.detail-content {
  flex: 1;
}

.detail-primary {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.25rem;
}

.detail-secondary {
  font-size: 1rem;
  color: #666;
}

.event-description {
  margin-bottom: 2rem;
}

.event-description h3 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1.3rem;
  font-weight: 600;
}

.event-description p {
  margin: 0;
  color: #555;
  line-height: 1.6;
  font-size: 1rem;
}

.event-footer {
  border-top: 1px solid #e9ecef;
  padding-top: 1.5rem;
  text-align: center;
}

.footer-text {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
  font-style: italic;
}

.loading {
  text-align: center;
  color: white;
  font-size: 1.2rem;
  padding: 3rem;
}

.error-state {
  text-align: center;
  color: white;
  padding: 3rem;
}

.error-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.error-state h2 {
  margin: 0 0 1rem 0;
  font-size: 2rem;
  font-weight: 600;
}

.error-state p {
  margin: 0;
  font-size: 1.1rem;
  opacity: 0.9;
}

/* Responsive design */
@media (max-width: 768px) {
  .public-event-container {
    padding: 1rem;
    align-items: flex-start;
    padding-top: 2rem;
  }
  
  .public-event-card {
    padding: 2rem;
  }
  
  .event-header {
    flex-direction: column;
    align-items: stretch;
    text-align: center;
  }
  
  .event-title {
    font-size: 1.8rem;
    margin-bottom: 1.5rem;
  }
  
  .detail-item {
    gap: 0.75rem;
  }
  
  .detail-icon {
    font-size: 1.3rem;
  }
  
  .detail-primary {
    font-size: 1rem;
  }
  
  .detail-secondary {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .public-event-card {
    padding: 1.5rem;
  }
  
  .event-title {
    font-size: 1.5rem;
  }
  
  .event-details {
    gap: 1.25rem;
  }
  
  .error-state {
    padding: 2rem 1rem;
  }
  
  .error-icon {
    font-size: 3rem;
  }
  
  .error-state h2 {
    font-size: 1.5rem;
  }
}
