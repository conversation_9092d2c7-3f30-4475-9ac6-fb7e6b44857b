import express from "express";
import dotenv from "dotenv";
import cors from "cors";
import helmet from "helmet";
import morgan from "morgan";


import connectDB from "./config/database.js";
import authRoutes from "./routes/auth.js";
import eventRoutes from "./routes/events.js";
import { error } from "console";



const app = express();
const PORT = process.env.PORT || 5000;
dotenv.config({
  path: "./.env"
})


// Middleware
app.use(helmet());
app.use(
  cors({
    origin: process.env.FRONTEND_URL || "http://localhost:5173",
    credentials: true,
  })
);
app.use(morgan("combined"));
app.use(express.json());
app.use(express.urlencoded({
  extended: true
}));


// Routes
app.use("/api/auth", authRoutes);
app.use("/api/events", eventRoutes);

// Error handling middleware
app.use((err, req, res, next) =>{
  console.error(err.stack);
  return res.status(500).json({
    error: "Something went wrong!",
    message: 
      process.env.NODE_ENV === "development" ? err.message
      : "Internal Server Error",
  })
})

// 404 handler
app.use((req, res) =>{
  return res.status(404).json({
    error: "Route not found!"
  });
});


connectDB().then(() => {
app.listen(PORT, () =>{
  console.log(`Server is running on port ${PORT}`)
})
}).catch((error) =>{
  console.error("Error connecting to database:", error);
})
