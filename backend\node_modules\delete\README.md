# delete [![NPM version](https://img.shields.io/npm/v/delete.svg?style=flat)](https://www.npmjs.com/package/delete) [![NPM monthly downloads](https://img.shields.io/npm/dm/delete.svg?style=flat)](https://npmjs.org/package/delete) [![NPM total downloads](https://img.shields.io/npm/dt/delete.svg?style=flat)](https://npmjs.org/package/delete) [![Linux Build Status](https://img.shields.io/travis/jonschlinkert/delete.svg?style=flat&label=Travis)](https://travis-ci.org/jonschlinkert/delete)

> Delete files and folders and any intermediate directories if they exist (sync and async).

## Install

Install with [npm](https://www.npmjs.com/):

```sh
$ npm install --save delete
```

## Usage

```js
var del = require('delete');

// async
del(['foo/*.js'], function(err, deleted) {
  if (err) throw err;
  // deleted files
  console.log(deleted);
});

// sync
del.sync(['foo/*.js']);

// promise
del.promise(['foo/*.js'])
  .then(function(deleted) {
    // deleted files
    console.log(deleted)
  });
```

## Options

All methods take an `options` object as the second argument.

### options.force

**Type**: `boolean`

**Default**: `undefined`

By default, error is thrown if you try to delete either the current working directory itself, or files outside of the current working directory (e.g. parent directories).

**Examples**

```js
del.sync('../foo.md', {force: true});

del('.', {force: true}, function(err, files) {
  // do stuff with err
  console.log(files);
});

del.promise('./', {force: true})
  .then(function(files) {
    console.log(files);
  })
```

_(This option was inspired by settings in [grunt](http://gruntjs.com/).)_

## About

### Related projects

* [copy](https://www.npmjs.com/package/copy): Copy files or directories using globs. | [homepage](https://github.com/jonschlinkert/copy "Copy files or directories using globs.")
* [export-files](https://www.npmjs.com/package/export-files): node.js utility for exporting a directory of files as modules. | [homepage](https://github.com/jonschlinkert/export-files "node.js utility for exporting a directory of files as modules.")
* [micromatch](https://www.npmjs.com/package/micromatch): Glob matching for javascript/node.js. A drop-in replacement and faster alternative to minimatch and multimatch. | [homepage](https://github.com/micromatch/micromatch "Glob matching for javascript/node.js. A drop-in replacement and faster alternative to minimatch and multimatch.")
* [write](https://www.npmjs.com/package/write): Write files to disk, creating intermediate directories if they don't exist. | [homepage](https://github.com/jonschlinkert/write "Write files to disk, creating intermediate directories if they don't exist.")

### Contributing

Pull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).

### Building docs

_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_

To generate the readme, run the following command:

```sh
$ npm install -g verbose/verb#dev verb-generate-readme && verb
```

### Running tests

Running and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:

```sh
$ npm install && npm test
```

### Author

**Jon Schlinkert**

* [github/jonschlinkert](https://github.com/jonschlinkert)
* [twitter/jonschlinkert](https://twitter.com/jonschlinkert)

### License

Copyright © 2017, [Jon Schlinkert](https://github.com/jonschlinkert).
Released under the [MIT License](LICENSE).

***

_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.6.0, on July 02, 2017._