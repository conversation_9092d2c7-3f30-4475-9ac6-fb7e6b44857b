# is-valid-glob [![NPM version](https://img.shields.io/npm/v/is-valid-glob.svg?style=flat)](https://www.npmjs.com/package/is-valid-glob) [![NPM monthly downloads](https://img.shields.io/npm/dm/is-valid-glob.svg?style=flat)](https://npmjs.org/package/is-valid-glob) [![NPM total downloads](https://img.shields.io/npm/dt/is-valid-glob.svg?style=flat)](https://npmjs.org/package/is-valid-glob) [![Linux Build Status](https://img.shields.io/travis/jonschlinkert/is-valid-glob.svg?style=flat&label=Travis)](https://travis-ci.org/jonschlinkert/is-valid-glob)

> Return true if a value is a valid glob pattern or patterns.

## Install

Install with [npm](https://www.npmjs.com/):

```sh
$ npm install --save is-valid-glob
```

## Usage

This really just checks to make sure that a pattern is either a string or array, and if it's an array it's either empty or consists of only strings.

```js
var isValidGlob = require('is-valid-glob');

isValidGlob('foo/*.js');
//=> true
```

**Valid patterns**

```js
isValidGlob('a');
isValidGlob('a.js');
isValidGlob('*.js');
isValidGlob(['a', 'b']);
//=> all true
```

**Invalid patterns**

```js
isValidGlob();
isValidGlob('');
isValidGlob(null);
isValidGlob(undefined);
isValidGlob(new Buffer('foo'));
isValidGlob(['foo', [[]]]);
isValidGlob(['foo', [['bar']]]);
isValidGlob(['foo', {}]);
isValidGlob({});
isValidGlob([]);
isValidGlob(['']);
//=> all false
```

## About

### Related projects

* [is-glob](https://www.npmjs.com/package/is-glob): Returns `true` if the given string looks like a glob pattern or an extglob pattern… [more](https://github.com/jonschlinkert/is-glob) | [homepage](https://github.com/jonschlinkert/is-glob "Returns `true` if the given string looks like a glob pattern or an extglob pattern. This makes it easy to create code that only uses external modules like node-glob when necessary, resulting in much faster code execution and initialization time, and a bet")
* [micromatch](https://www.npmjs.com/package/micromatch): Glob matching for javascript/node.js. A drop-in replacement and faster alternative to minimatch and multimatch. | [homepage](https://github.com/micromatch/micromatch "Glob matching for javascript/node.js. A drop-in replacement and faster alternative to minimatch and multimatch.")
* [vinyl-fs](https://www.npmjs.com/package/vinyl-fs): Vinyl adapter for the file system | [homepage](http://github.com/wearefractal/vinyl-fs "Vinyl adapter for the file system")
* [vinyl](https://www.npmjs.com/package/vinyl): Virtual file format. | [homepage](https://github.com/gulpjs/vinyl#readme "Virtual file format.")

### Contributing

Pull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).

### Contributors

| **Commits** | **Contributor** | 
| --- | --- |
| 9 | [jonschlinkert](https://github.com/jonschlinkert) |
| 2 | [contra](https://github.com/contra) |

### Building docs

_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_

To generate the readme, run the following command:

```sh
$ npm install -g verbose/verb#dev verb-generate-readme && verb
```

### Running tests

Running and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:

```sh
$ npm install && npm test
```

### Author

**Jon Schlinkert**

* [github/jonschlinkert](https://github.com/jonschlinkert)
* [twitter/jonschlinkert](https://twitter.com/jonschlinkert)

### License

Copyright © 2017, [Jon Schlinkert](https://github.com/jonschlinkert).
Released under the [MIT License](LICENSE).

***

_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.6.0, on June 21, 2017._