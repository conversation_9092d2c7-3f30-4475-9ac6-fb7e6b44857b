# has-glob [![NPM version](https://img.shields.io/npm/v/has-glob.svg?style=flat)](https://www.npmjs.com/package/has-glob) [![NPM downloads](https://img.shields.io/npm/dm/has-glob.svg?style=flat)](https://npmjs.org/package/has-glob) [![Build Status](https://img.shields.io/travis/jonschlinkert/has-glob.svg?style=flat)](https://travis-ci.org/jonschlinkert/has-glob)

> Returns `true` if an array has a glob pattern.

## Install

Install with [npm](https://www.npmjs.com/):

```sh
$ npm install --save has-glob
```

## Usage

```js
var hasGlob = require('has-glob');

hasGlob(['foo', 'bar', '*.js']);
//=> true

hasGlob(['foo', 'bar', 'baz']);
//=> false
```

**True**

The following all return `true`:

```js
hasGlob(['foo', 'bar', '*.js'])
hasGlob(['foo', 'bar', '!*.js'])
hasGlob(['foo', 'bar', '!foo'])
hasGlob(['foo', 'bar', '!foo.js'])
hasGlob(['foo', 'bar', '**/abc.js'])
hasGlob(['foo', 'bar', 'abc/*.js'])
hasGlob(['foo', 'bar', 'abc/{a,b}.js'])
hasGlob(['foo', 'bar', 'abc/{a..z}.js'])
hasGlob(['foo', 'bar', 'abc/{a..z..2}.js'])
hasGlob(['foo', 'bar', 'abc/@(a).js'])
hasGlob(['foo', 'bar', 'abc/!(a).js'])
hasGlob(['foo', 'bar', 'abc/+(a).js'])
hasGlob(['foo', 'bar', 'abc/*(a).js'])
hasGlob(['foo', 'bar', 'abc/?(a).js'])
hasGlob(['foo', 'bar', 'abc/@.js'])
hasGlob(['foo', 'bar', 'abc/!.js'])
hasGlob(['foo', 'bar', 'abc/+.js'])
hasGlob(['foo', 'bar', 'abc/*.js'])
hasGlob(['foo', 'bar', 'abc/?.js'])
hasGlob(['foo', 'bar', 'abc/(aaa|bbb).js'])
hasGlob(['foo', 'bar', 'abc/?.js'])
hasGlob(['foo', 'bar', '?.js'])
hasGlob(['foo', 'bar', '[abc].js'])
hasGlob(['foo', 'bar', '[^abc].js'])
hasGlob(['foo', 'bar', 'a/b/c/[a-z].js'])
hasGlob(['foo', 'bar', '[a-j]*[^c]b/c'])
//=> true
```

Also works with strings:

```js
hasGlob('*.js')
hasGlob('!*.js')
hasGlob('!foo')
hasGlob('!foo.js')
hasGlob('**/abc.js')
hasGlob('abc/*.js')
//=> true
```

**False**

The following all return `false`:

```js
hasGlob([])
hasGlob([null])
hasGlob([undefined])
hasGlob([{}])
hasGlob(['.'])
hasGlob(['foo', 'bar', 'aa'])
hasGlob(['foo', 'bar', 'abc.js'])
hasGlob(['foo', 'bar', 'abc/def/ghi.js'])
//=> false
```

## About

### Related projects

* [has-glob](https://www.npmjs.com/package/has-glob): Returns `true` if an array has a glob pattern. | [homepage](https://github.com/jonschlinkert/has-glob "Returns `true` if an array has a glob pattern.")
* [is-glob](https://www.npmjs.com/package/is-glob): Returns `true` if the given string looks like a glob pattern or an extglob pattern… [more](https://github.com/jonschlinkert/is-glob) | [homepage](https://github.com/jonschlinkert/is-glob "Returns `true` if the given string looks like a glob pattern or an extglob pattern. This makes it easy to create code that only uses external modules like node-glob when necessary, resulting in much faster code execution and initialization time, and a bet")
* [is-negated-glob](https://www.npmjs.com/package/is-negated-glob): Returns an object with a `negated` boolean and the `!` stripped from negation patterns. Also… [more](https://github.com/jonschlinkert/is-negated-glob) | [homepage](https://github.com/jonschlinkert/is-negated-glob "Returns an object with a `negated` boolean and the `!` stripped from negation patterns. Also respects extglobs.")
* [is-valid-glob](https://www.npmjs.com/package/is-valid-glob): Return true if a value is a valid glob pattern or patterns. | [homepage](https://github.com/jonschlinkert/is-valid-glob "Return true if a value is a valid glob pattern or patterns.")
* [micromatch](https://www.npmjs.com/package/micromatch): Glob matching for javascript/node.js. A drop-in replacement and faster alternative to minimatch and multimatch. | [homepage](https://github.com/jonschlinkert/micromatch "Glob matching for javascript/node.js. A drop-in replacement and faster alternative to minimatch and multimatch.")

### Contributing

Pull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).

### Building docs

_(This document was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme) (a [verb](https://github.com/verbose/verb) generator), please don't edit the readme directly. Any changes to the readme must be made in [.verb.md](.verb.md).)_

To generate the readme and API documentation with [verb](https://github.com/verbose/verb):

```sh
$ npm install -g verb verb-generate-readme && verb
```

### Running tests

Install dev dependencies:

```sh
$ npm install -d && npm test
```

### Author

**Jon Schlinkert**

* [github/jonschlinkert](https://github.com/jonschlinkert)
* [twitter/jonschlinkert](http://twitter.com/jonschlinkert)

### License

Copyright © 2016, [Jon Schlinkert](https://github.com/jonschlinkert).
Released under the [MIT license](https://github.com/jonschlinkert/has-glob/blob/master/LICENSE).

***

_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.1.30, on September 08, 2016._