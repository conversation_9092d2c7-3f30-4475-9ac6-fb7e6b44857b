import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { eventsAPI } from '../services/api';
import { formatDateTime, getRelativeTime, isUpcoming } from '../utils/dateUtils';
import './PublicEvent.css';

const PublicEvent = () => {
  const { shareId } = useParams();
  const [event, setEvent] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchPublicEvent();
  }, [shareId]);

  const fetchPublicEvent = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await eventsAPI.getPublicEvent(shareId);
      setEvent(response.data.event);
    } catch (error) {
      console.error('Error fetching public event:', error);
      if (error.response?.status === 404) {
        setError('Event not found or is no longer public.');
      } else {
        setError('Failed to load event. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="public-event-container">
        <div className="loading">Loading event...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="public-event-container">
        <div className="error-state">
          <div className="error-icon">❌</div>
          <h2>Event Not Found</h2>
          <p>{error}</p>
        </div>
      </div>
    );
  }

  if (!event) {
    return (
      <div className="public-event-container">
        <div className="error-state">
          <div className="error-icon">❌</div>
          <h2>Event Not Found</h2>
          <p>This event does not exist or is no longer public.</p>
        </div>
      </div>
    );
  }

  const eventIsUpcoming = isUpcoming(event.eventDate);

  return (
    <div className="public-event-container">
      <div className="public-event-card">
        <div className="event-header">
          <div className="event-badge">
            <span className="badge-icon">🔗</span>
            <span>Public Event</span>
          </div>
          
          <div className={`event-status ${eventIsUpcoming ? 'upcoming' : 'past'}`}>
            {eventIsUpcoming ? 'Upcoming' : 'Past Event'}
          </div>
        </div>

        <h1 className="event-title">{event.title}</h1>

        <div className="event-details">
          <div className="detail-item">
            <div className="detail-icon">📅</div>
            <div className="detail-content">
              <div className="detail-primary">{formatDateTime(event.eventDate)}</div>
              <div className="detail-secondary">{getRelativeTime(event.eventDate)}</div>
            </div>
          </div>

          {event.location && (
            <div className="detail-item">
              <div className="detail-icon">📍</div>
              <div className="detail-content">
                <div className="detail-primary">{event.location}</div>
              </div>
            </div>
          )}

          {event.user && (event.user.firstName || event.user.lastName) && (
            <div className="detail-item">
              <div className="detail-icon">👤</div>
              <div className="detail-content">
                <div className="detail-primary">
                  Organized by {event.user.firstName} {event.user.lastName}
                </div>
              </div>
            </div>
          )}
        </div>

        {event.description && (
          <div className="event-description">
            <h3>Description</h3>
            <p>{event.description}</p>
          </div>
        )}

        <div className="event-footer">
          <p className="footer-text">
            This is a public event shared via Mini Event Tracker
          </p>
        </div>
      </div>
    </div>
  );
};

export default PublicEvent;
