{"name": "global-prefix", "description": "Get the npm global path prefix.", "version": "1.0.2", "homepage": "https://github.com/jonschlinkert/global-prefix", "author": "<PERSON> (https://github.com/jonschlinkert)", "contributors": ["<PERSON><PERSON><PERSON> (https://github.com/rmbaad)", "<PERSON> (https://twitter.com/doowb)", "Charl<PERSON> Reagent (https://i.am.charlike.online)", "<PERSON><PERSON><PERSON> (https://packagist.org/packages/jason-chang)", "<PERSON> (http://twitter.com/jonschlink<PERSON>)", "<PERSON><PERSON><PERSON> (https://www.ncode.nl)", "<PERSON> (chrome://dino)", "<PERSON> (http://rossfenning.co.uk)"], "repository": "jonschlinkert/global-prefix", "bugs": {"url": "https://github.com/jonschlinkert/global-prefix/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"expand-tilde": "^2.0.2", "homedir-polyfill": "^1.0.1", "ini": "^1.3.4", "is-windows": "^1.0.1", "which": "^1.2.14"}, "devDependencies": {"gulp-format-md": "^0.1.12", "mocha": "^3.4.2"}, "keywords": ["global", "module", "modules", "npm", "path", "prefix", "resolve"], "verb": {"run": true, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["global-modules", "global-paths"]}, "reflinks": ["verb"], "lint": {"reflinks": true}}}