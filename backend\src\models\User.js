import mongoose from "mongoose";
import bcrypt from  "bcryptjs";

const userSchema = new mongoose.Schema({
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true,
    validate: {
      validator: function (eamil){
        return /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/.test(email);
      },
      message: "Please provide a valid email",
    },
    maxlength: 255,
    minlength: 5,
  },
  password: {
    type: String,
    required: true,
    minlength: 6,
    maxlength: 255,
  },
  firstName: {
    type: String,
    trim: true,
    maxlength: 50,
    minlength: 1
  },
  lastName: {
    type: String,
    trim: true,
    maxlength: 50,
    minlength: 1,
  },
}, {timestamps: true,
  toJSON: {
    transform: function (doc, ret) {
      delete ret.password;
      return ret;
    },
  }
});



userSchema.pre("save", async function(next) {
  if (!this.isModified("password")) return next();

  try {
    const rounds = parseInt(process.env.BCRYPT_ROUNDS) ||
    12;
    this.password = await bcrypt.hash(this.password, rounds);
    next();
  } catch (error) {
    next(error);
  }
});

// Validate password
userSchema.methods.validatePassword = async function (password) {
  return await bcrypt.compare(password, this.password);
};



const User = mongoose.model("User", userSchema);

export default User;

